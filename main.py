import weaviate
import weaviate.classes as wvc
import os
from pathlib import Path
from dotenv import load_dotenv
# Replace unstructured with PyPDF2
import pypdf

# Load environment variables
load_dotenv()
def connect_to_weaviate():
    client = weaviate.connect_to_weaviate_cloud(
        cluster_url=os.getenv("WEAVIATE_CLUSTER_URL"),
        auth_credentials=weaviate.auth.AuthApiKey(os.getenv("WEAVIATE_API_KEY")),
        headers={
            "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY")
        }
    )
    return client
def get_or_create_collection(client, collection_name):
    if client.collections.exists(collection_name):
        collection = client.collections.get(collection_name)
        return collection
    else:
        collection = client.collections.create(
            name=collection_name,
            vectorizer_config=wvc.config.Configure.Vectorizer.text2vec_openai(
                model="text-embedding-3-small"
            ),
            generative_config=wvc.config.Configure.Generative.openai(
                model="gpt-4o-mini"
            ),
            inverted_index_config=wvc.config.Configure.inverted_index(
                index_null_state=True
            ),
            properties=[
                wvc.config.Property(
                    name="data",
                    data_type=wvc.config.DataType.TEXT,
                    tokenization=wvc.config.Tokenization.LOWERCASE,
                ),
            ],
        )
        return collection
def extract_pdf_pages(pdf_path):
    pages_text = []
    try:
        # Use PyPDF2 instead of unstructured
        reader = pypdf.PdfReader(pdf_path)
        
        # Extract text from each page
        for page_num, page in enumerate(reader.pages, 1):
            text = page.extract_text()
            if text.strip():  # Only add non-empty pages
                pages_text.append(text)
        
        # Print sample text for verification
        if pages_text:
            print(f"Sample text: {pages_text[0][:100]}")
            
    except Exception as e:
        print(f"Error extracting PDF: {e}")
    
    return pages_text
def insert_pdf_to_weaviate(pdf_path, collection_name):
    # Connect to Weaviate Cloud
    client = connect_to_weaviate()
    try:
        # Extract pages
        pages_text = extract_pdf_pages(pdf_path)
        # Get collection
        collection = get_or_create_collection(client, collection_name)
        # Insert each page
        for page_num, page_text in enumerate(pages_text, 1):
            data_object = {
                "data": page_text
            }
            collection.data.insert(data_object)
        return len(pages_text)
    finally:
        client.close()
def main():
    PDF_PATH = "D:/pdf/b.pdf"  # Update this path
    COLLECTION_NAME = "Class_9_Bangla_Book"
    total_pages = insert_pdf_to_weaviate(PDF_PATH, COLLECTION_NAME)
    print(f"Inserted {total_pages} pages successfully!")
if __name__ == "__main__":
    main()
