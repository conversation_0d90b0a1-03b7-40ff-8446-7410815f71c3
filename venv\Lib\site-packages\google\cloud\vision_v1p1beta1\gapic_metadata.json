{"comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "python", "libraryPackage": "google.cloud.vision_v1p1beta1", "protoPackage": "google.cloud.vision.v1p1beta1", "schema": "1.0", "services": {"ImageAnnotator": {"clients": {"grpc": {"libraryClient": "ImageAnnotatorClient", "rpcs": {"BatchAnnotateImages": {"methods": ["batch_annotate_images"]}}}, "grpc-async": {"libraryClient": "ImageAnnotatorAsyncClient", "rpcs": {"BatchAnnotateImages": {"methods": ["batch_annotate_images"]}}}, "rest": {"libraryClient": "ImageAnnotatorClient", "rpcs": {"BatchAnnotateImages": {"methods": ["batch_annotate_images"]}}}}}}}