# -*- coding: utf-8 -*-
# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from google.cloud.vision import gapic_version as package_version

__version__ = package_version.__version__


from google.cloud.vision_v1.services.image_annotator.async_client import (
    ImageAnnotatorAsyncClient,
)
from google.cloud.vision_v1 import ImageAnnotatorClient
from google.cloud.vision_v1.services.product_search.async_client import (
    ProductSearchAsyncClient,
)
from google.cloud.vision_v1.services.product_search.client import ProductSearchClient
from google.cloud.vision_v1.types.geometry import (
    <PERSON><PERSON>ing<PERSON>oly,
    NormalizedVertex,
    Position,
    Vertex,
)
from google.cloud.vision_v1.types.image_annotator import (
    AnnotateFileRequest,
    AnnotateFileResponse,
    AnnotateImageRequest,
    AnnotateImageResponse,
    AsyncAnnotateFileRequest,
    AsyncAnnotateFileResponse,
    AsyncBatchAnnotateFilesRequest,
    AsyncBatchAnnotateFilesResponse,
    AsyncBatchAnnotateImagesRequest,
    AsyncBatchAnnotateImagesResponse,
    BatchAnnotateFilesRequest,
    BatchAnnotateFilesResponse,
    BatchAnnotateImagesRequest,
    BatchAnnotateImagesResponse,
    ColorInfo,
    CropHint,
    CropHintsAnnotation,
    CropHintsParams,
    DominantColorsAnnotation,
    EntityAnnotation,
    FaceAnnotation,
    Feature,
    GcsDestination,
    GcsSource,
    Image,
    ImageAnnotationContext,
    ImageContext,
    ImageProperties,
    ImageSource,
    InputConfig,
    LatLongRect,
    Likelihood,
    LocalizedObjectAnnotation,
    LocationInfo,
    OperationMetadata,
    OutputConfig,
    Property,
    SafeSearchAnnotation,
    TextDetectionParams,
    WebDetectionParams,
)
from google.cloud.vision_v1.types.product_search import (
    ProductSearchParams,
    ProductSearchResults,
)
from google.cloud.vision_v1.types.product_search_service import (
    AddProductToProductSetRequest,
    BatchOperationMetadata,
    CreateProductRequest,
    CreateProductSetRequest,
    CreateReferenceImageRequest,
    DeleteProductRequest,
    DeleteProductSetRequest,
    DeleteReferenceImageRequest,
    GetProductRequest,
    GetProductSetRequest,
    GetReferenceImageRequest,
    ImportProductSetsGcsSource,
    ImportProductSetsInputConfig,
    ImportProductSetsRequest,
    ImportProductSetsResponse,
    ListProductSetsRequest,
    ListProductSetsResponse,
    ListProductsInProductSetRequest,
    ListProductsInProductSetResponse,
    ListProductsRequest,
    ListProductsResponse,
    ListReferenceImagesRequest,
    ListReferenceImagesResponse,
    Product,
    ProductSet,
    ProductSetPurgeConfig,
    PurgeProductsRequest,
    ReferenceImage,
    RemoveProductFromProductSetRequest,
    UpdateProductRequest,
    UpdateProductSetRequest,
)
from google.cloud.vision_v1.types.text_annotation import (
    Block,
    Page,
    Paragraph,
    Symbol,
    TextAnnotation,
    Word,
)
from google.cloud.vision_v1.types.web_detection import WebDetection

__all__ = (
    "ImageAnnotatorClient",
    "ImageAnnotatorAsyncClient",
    "ProductSearchClient",
    "ProductSearchAsyncClient",
    "BoundingPoly",
    "NormalizedVertex",
    "Position",
    "Vertex",
    "AnnotateFileRequest",
    "AnnotateFileResponse",
    "AnnotateImageRequest",
    "AnnotateImageResponse",
    "AsyncAnnotateFileRequest",
    "AsyncAnnotateFileResponse",
    "AsyncBatchAnnotateFilesRequest",
    "AsyncBatchAnnotateFilesResponse",
    "AsyncBatchAnnotateImagesRequest",
    "AsyncBatchAnnotateImagesResponse",
    "BatchAnnotateFilesRequest",
    "BatchAnnotateFilesResponse",
    "BatchAnnotateImagesRequest",
    "BatchAnnotateImagesResponse",
    "ColorInfo",
    "CropHint",
    "CropHintsAnnotation",
    "CropHintsParams",
    "DominantColorsAnnotation",
    "EntityAnnotation",
    "FaceAnnotation",
    "Feature",
    "GcsDestination",
    "GcsSource",
    "Image",
    "ImageAnnotationContext",
    "ImageContext",
    "ImageProperties",
    "ImageSource",
    "InputConfig",
    "LatLongRect",
    "LocalizedObjectAnnotation",
    "LocationInfo",
    "OperationMetadata",
    "OutputConfig",
    "Property",
    "SafeSearchAnnotation",
    "TextDetectionParams",
    "WebDetectionParams",
    "Likelihood",
    "ProductSearchParams",
    "ProductSearchResults",
    "AddProductToProductSetRequest",
    "BatchOperationMetadata",
    "CreateProductRequest",
    "CreateProductSetRequest",
    "CreateReferenceImageRequest",
    "DeleteProductRequest",
    "DeleteProductSetRequest",
    "DeleteReferenceImageRequest",
    "GetProductRequest",
    "GetProductSetRequest",
    "GetReferenceImageRequest",
    "ImportProductSetsGcsSource",
    "ImportProductSetsInputConfig",
    "ImportProductSetsRequest",
    "ImportProductSetsResponse",
    "ListProductSetsRequest",
    "ListProductSetsResponse",
    "ListProductsInProductSetRequest",
    "ListProductsInProductSetResponse",
    "ListProductsRequest",
    "ListProductsResponse",
    "ListReferenceImagesRequest",
    "ListReferenceImagesResponse",
    "Product",
    "ProductSet",
    "ProductSetPurgeConfig",
    "PurgeProductsRequest",
    "ReferenceImage",
    "RemoveProductFromProductSetRequest",
    "UpdateProductRequest",
    "UpdateProductSetRequest",
    "Block",
    "Page",
    "Paragraph",
    "Symbol",
    "TextAnnotation",
    "Word",
    "WebDetection",
)
